// Practice translating js to ts

function createUser(name, email, age) {
    return {
        id: Math.random().toString(36).substr(2, 9),
        name: name,
        email: email,
        age: age,
        isActive: true,
        createdAt: new Date()
    };
}

function validateEmail(email) {
    return email.includes('@') && email.includes('.');
}

function updateUserProfile(user, updates) {
    return {
        ...user,
        ...updates,
        updatedAt: new Date()
    };
}

function getUsers() {
    return fetch('/api/users')
        .then(response => response.json())
        .catch(error => {
            console.error('Failed to fetch users:', error);
            return [];
        });
}

const defaultUser = createUser('Guest', '<EMAIL>', 0);

const users = [
    createUser('Alice', '<EMAIL>', 25),
    createUser('Bob', '<EMAIL>', 30),
    createUser('Charlie', '<EMAIL>', 35)
];