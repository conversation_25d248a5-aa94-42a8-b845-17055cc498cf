import { Image } from 'expo-image';
import { router } from 'expo-router';
import { useState } from 'react';
import { ActivityIndicator, Platform, Pressable, StyleSheet } from 'react-native';

import { HelloWave } from '@/components/HelloWave';
import ParallaxScrollView from '@/components/ParallaxScrollView';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';

// Define the type for cat data
type CatImage = {
  id: string;
  url: string;
  width: number;
  height: number;
};

// Define types for voting
type Vote = {
  id: number;
  image_id: string;
  value: number; // 1 for upvote, 0 for downvote
  created_at: string;
};

type CatWithVotes = CatImage & {
  userVote?: number; // 1 for upvote, 0 for downvote, undefined for no vote
  voteId?: number; // ID of the vote for deletion
};

export default function HomeScreen() {
  // State for cat images and loading
  const [catImages, setCatImages] = useState<CatWithVotes[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [votes, setVotes] = useState<Vote[]>([]);

  // API key for Cat API (you'll need to get this from the Cat API dashboard)
  const API_KEY = 'live_ylX4blBYT9FaoVd6OhvR';

  // Fetch cat images !!!
  const fetchCatImages = async () => {
    // setIsLoading(true); 
    // setError(null);
    try {
      const response = await fetch('https://api.thecatapi.com/v1/images/search?limit=3');
      const data: CatImage[] = await response.json();

      // Add new cats to existing ones (so they accumulate)
      setCatImages(prevCats => [...prevCats, ...data]);

    } catch (err) {
      // Handle any errors
      setError(err instanceof Error ? err.message : 'Something went wrong!');
    } finally {
      // Always stop loading
      setIsLoading(false);
    }
  };

  // Function to vote on a cat image
  const voteOnCat = async (imageId: string, value: number) => {
    try {
      const response = await fetch('https://api.thecatapi.com/v1/votes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': API_KEY,
        },
        body: JSON.stringify({
          image_id: imageId,
          value: value, // 1 for upvote, 0 for downvote
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to vote: ${response.status}`);
      }

      const result = await response.json();

      // Update the cat image with the vote
      setCatImages(prevCats =>
        prevCats.map(cat =>
          cat.id === imageId
            ? { ...cat, userVote: value, voteId: result.id }
            : cat
        )
      );

      // Fetch updated vote history
      await fetchVoteHistory();

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to vote');
    }
  };

  // Function to fetch vote history
  const fetchVoteHistory = async () => {
    try {
      const response = await fetch('https://api.thecatapi.com/v1/votes', {
        headers: {
          'x-api-key': API_KEY,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch votes: ${response.status}`);
      }

      const votesData: Vote[] = await response.json();
      setVotes(votesData);

      // Update cat images with their vote status
      setCatImages(prevCats =>
        prevCats.map(cat => {
          const vote = votesData.find(v => v.image_id === cat.id);
          return vote
            ? { ...cat, userVote: vote.value, voteId: vote.id }
            : cat;
        })
      );

    } catch (err) {
      console.log('Failed to fetch vote history:', err);
    }
  };

  // Function to remove a vote
  const removeVote = async (voteId: number, imageId: string) => {
    try {
      const response = await fetch(`https://api.thecatapi.com/v1/votes/${voteId}`, {
        method: 'DELETE',
        headers: {
          'x-api-key': API_KEY,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to remove vote: ${response.status}`);
      }

      // Update the cat image to remove the vote
      setCatImages(prevCats =>
        prevCats.map(cat =>
          cat.id === imageId
            ? { ...cat, userVote: undefined, voteId: undefined }
            : cat
        )
      );

      // Fetch updated vote history
      await fetchVoteHistory();

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to remove vote');
    }
  };

  return (
    <ParallaxScrollView
      headerBackgroundColor={{ light: '#A1CEDC', dark: '#1D3D47' }}
      headerImage={
        <Image
          source={require('@/assets/images/partial-react-logo.png')}
          style={styles.reactLogo}
        />
      }>
      <ThemedView style={styles.titleContainer}>
        <ThemedText type="title">hello how are you</ThemedText>
        <HelloWave />
      </ThemedView>
      <ThemedView style={styles.stepContainer}>
        <ThemedText type="subtitle">Step 1: Try it</ThemedText>
        <ThemedText>
          Edit <ThemedText type="defaultSemiBold">app/(tabs)/index.tsx</ThemedText> to see changes.
          Press{' '}
          <ThemedText type="defaultSemiBold">
            {Platform.select({
              ios: 'cmd + d',
              android: 'cmd + m',
              web: 'F12',
            })}
          </ThemedText>{' '}
          to open developer tools.
        </ThemedText>
      </ThemedView>
      <ThemedView style={{flexDirection: 'row', alignItems: 'center',gap: 8,}}>
        <ThemedText type="subtitle">Step 2: Explore</ThemedText>
        <ThemedText>
          {`Tap the Explore tab to learn more about what's included in this starter app.`}
        </ThemedText>
      </ThemedView>
      <ThemedView style={styles.stepContainer}>
        <ThemedText type="subtitle">Step 3: Get a fresh start</ThemedText>
        <ThemedText>
          {`When you're ready, run `}
          <ThemedText type="defaultSemiBold">npm run reset-project</ThemedText> to get a fresh{' '}
          <ThemedText type="defaultSemiBold">app</ThemedText> directory. This will move the current{' '}
          <ThemedText type="defaultSemiBold">app</ThemedText> to{' '}
          <ThemedText type="defaultSemiBold">app-example</ThemedText>.
        </ThemedText>
      </ThemedView>

      <ThemedView style={styles.stepContainer}>
        <ThemedText type="subtitle">🎨 StyleSheet Exercise</ThemedText>
        <ThemedText>
          Ready to practice styling? Tap the button below to go to the 404 page where you can practice your StyleSheet skills!
        </ThemedText>
        <Pressable
          style={styles.testButton}
          onPress={() => router.push('/nonexistent-page' as any)}
        >
          <ThemedText style={styles.testButtonText}>Go to StyleSheet Exercise Page</ThemedText>
        </Pressable>
      </ThemedView>

      <ThemedView style={styles.stepContainer}>
        <ThemedText type="subtitle">🐱 Fetch API Exercise</ThemedText>
        <ThemedText>
          Fetches random cat pictures from the internet.
        </ThemedText>
        <Pressable
          style={[styles.catButton, isLoading && styles.disabledButton]}
          onPress={fetchCatImages}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator color="white" size="small" />
          ) : (
            <ThemedText style={styles.testButtonText}>
              🐱 Fetch Cat Pictures ({catImages.length})
            </ThemedText>
          )}
        </Pressable>

        {error && (
          <ThemedView style={styles.errorContainer}>
            <ThemedText style={styles.errorText}>❌ Error: {error}</ThemedText>
          </ThemedView>
        )}

        <ThemedText style={styles.apiInfo}>
          API: https://api.thecatapi.com/v1/images/search
        </ThemedText>
      </ThemedView>

      {/* Display Cat Images */}
      {catImages.length > 0 && (
        <ThemedView style={styles.catsContainer}>
          <ThemedText type="subtitle">🐾 Your Cat Collection</ThemedText>
          <ThemedView style={styles.catsGrid}>
            {catImages.map((cat, index) => (
              <ThemedView key={`${cat.id}-${index}`} style={styles.catImageContainer}>
                <Image
                  source={{ uri: cat.url }}
                  style={styles.catImage}
                />
                <ThemedText style={styles.catInfo}>
                  {cat.width} × {cat.height}
                </ThemedText>

                {/* Voting buttons */}
                <ThemedView style={styles.voteContainer}>
                  <Pressable
                    style={[
                      styles.voteButton,
                      styles.upvoteButton,
                      cat.userVote === 1 && styles.activeVote
                    ]}
                    onPress={() => {
                      if (cat.userVote === 1 && cat.voteId) {
                        removeVote(cat.voteId, cat.id);
                      } else {
                        voteOnCat(cat.id, 1);
                      }
                    }}
                  >
                    <ThemedText style={styles.voteText}>
                      👍 {cat.userVote === 1 ? 'Liked' : 'Like'}
                    </ThemedText>
                  </Pressable>

                  <Pressable
                    style={[
                      styles.voteButton,
                      styles.downvoteButton,
                      cat.userVote === 0 && styles.activeVote
                    ]}
                    onPress={() => {
                      if (cat.userVote === 0 && cat.voteId) {
                        removeVote(cat.voteId, cat.id);
                      } else {
                        voteOnCat(cat.id, 0);
                      }
                    }}
                  >
                    <ThemedText style={styles.voteText}>
                      👎 {cat.userVote === 0 ? 'Disliked' : 'Dislike'}
                    </ThemedText>
                  </Pressable>
                </ThemedView>
              </ThemedView>
            ))}
          </ThemedView>

          <ThemedView style={styles.actionButtons}>
            <Pressable
              style={styles.clearButton}
              onPress={() => setCatImages([])}
            >
              <ThemedText style={styles.clearButtonText}>🗑️ Clear All Cats</ThemedText>
            </Pressable>

            <Pressable
              style={styles.historyButton}
              onPress={fetchVoteHistory}
            >
              <ThemedText style={styles.clearButtonText}>📊 Refresh Votes</ThemedText>
            </Pressable>
          </ThemedView>
        </ThemedView>
      )}

      {/* Vote History */}
      {votes.length > 0 && (
        <ThemedView style={styles.voteHistoryContainer}>
          <ThemedText type="subtitle">📊 Your Vote History</ThemedText>
          <ThemedText style={styles.voteHistoryInfo}>
            Total votes: {votes.length} • Upvotes: {votes.filter(v => v.value === 1).length} • Downvotes: {votes.filter(v => v.value === 0).length}
          </ThemedText>

          <ThemedView style={styles.voteHistoryList}>
            {votes.slice(0, 5).map((vote) => (
              <ThemedView key={vote.id} style={styles.voteHistoryItem}>
                <ThemedText style={styles.voteHistoryText}>
                  {vote.value === 1 ? '👍' : '👎'} Image: {vote.image_id.substring(0, 8)}...
                </ThemedText>
                <ThemedText style={styles.voteHistoryDate}>
                  {new Date(vote.created_at).toLocaleDateString()}
                </ThemedText>
              </ThemedView>
            ))}
          </ThemedView>

          {votes.length > 5 && (
            <ThemedText style={styles.moreVotesText}>
              ... and {votes.length - 5} more votes
            </ThemedText>
          )}
        </ThemedView>
      )}

    </ParallaxScrollView>
  );
}

const styles = StyleSheet.create({
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  stepContainer: {
    gap: 8,
    marginBottom: 8,
  },
  reactLogo: {
    height: 178,
    width: 290,
    bottom: 0,
    left: 0,
    position: 'absolute',
  },
  testButton: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 12,
    alignItems: 'center',
  },
  testButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  catButton: {
    backgroundColor: '#FF6B6B',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 12,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
  },
  disabledButton: {
    backgroundColor: '#CCCCCC',
    opacity: 0.7,
  },
  errorContainer: {
    backgroundColor: '#FFE6E6',
    padding: 12,
    borderRadius: 8,
    marginTop: 12,
    borderWidth: 1,
    borderColor: '#FF6B6B',
  },
  errorText: {
    color: '#D32F2F',
    fontSize: 14,
    textAlign: 'center',
  },
  apiInfo: {
    fontSize: 12,
    fontFamily: 'monospace',
    marginTop: 8,
    textAlign: 'center',
    opacity: 0.7,
  },
  catsContainer: {
    marginTop: 20,
    padding: 16,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 107, 107, 0.1)',
  },
  catsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 12,
    marginTop: 16,
  },
  catImageContainer: {
    width: '48%',
    alignItems: 'center',
  },
  catImage: {
    width: '100%',
    height: 120,
    borderRadius: 8,
    marginBottom: 4,
  },
  catInfo: {
    fontSize: 12,
    opacity: 0.7,
    textAlign: 'center',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 16,
  },
  clearButton: {
    backgroundColor: '#757575',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    alignItems: 'center',
    flex: 1,
  },
  historyButton: {
    backgroundColor: '#9C27B0',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    alignItems: 'center',
    flex: 1,
  },
  clearButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  voteContainer: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 8,
  },
  voteButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    flex: 1,
    alignItems: 'center',
  },
  upvoteButton: {
    backgroundColor: '#4CAF50',
  },
  downvoteButton: {
    backgroundColor: '#F44336',
  },
  activeVote: {
    opacity: 0.7,
    borderWidth: 2,
    borderColor: '#FFD700',
  },
  voteText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  voteHistoryContainer: {
    marginTop: 20,
    padding: 16,
    borderRadius: 12,
    backgroundColor: 'rgba(156, 39, 176, 0.1)',
  },
  voteHistoryInfo: {
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
    opacity: 0.8,
  },
  voteHistoryList: {
    marginTop: 12,
    gap: 8,
  },
  voteHistoryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    borderRadius: 6,
  },
  voteHistoryText: {
    fontSize: 14,
    flex: 1,
  },
  voteHistoryDate: {
    fontSize: 12,
    opacity: 0.7,
  },
  moreVotesText: {
    fontSize: 12,
    textAlign: 'center',
    marginTop: 8,
    opacity: 0.7,
    fontStyle: 'italic',
  },
});
