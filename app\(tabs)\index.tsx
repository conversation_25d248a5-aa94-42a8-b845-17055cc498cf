import { Image } from 'expo-image';
import { router } from 'expo-router';
import { useEffect, useState } from 'react';
import { ActivityIndicator, Platform, Pressable, StyleSheet } from 'react-native';

import { HelloWave } from '@/components/HelloWave';
import ParallaxScrollView from '@/components/ParallaxScrollView';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';

// Define the type for cat data
type CatImage = {
  id: string;
  url: string;
  width: number;
  height: number;
};

// Define types for voting
type Vote = {
  id: number;
  image_id: string;
  value: number; // 1 for upvote
  created_at: string;
  sub_id?: string;
  country_code?: string;
  image?: {
    id: string;
    url: string;
  };
};

type CatWithVotes = CatImage & {
  userVote?: number; // 1 for upvote, undefined for no vote
  voteId?: number; // ID of the vote for deletion
  totalLikes?: number; // Total likes for this image
};

export default function HomeScreen() {
  // State for cat images and loading
  const [catImages, setCatImages] = useState<CatWithVotes[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [votes, setVotes] = useState<Vote[]>([]);

  // API key for Cat API (you'll need to get this from the Cat API dashboard)
  const API_KEY = 'live_jmJKproPDwPuztFPqIkgJN9RuONw1x8K4HK1VYV163bLbXweKHSUxFSbwVZZ3fM0';

  // Fetch vote counts when component loads
  useEffect(() => {
    fetchVoteCounts();
  }, []);

  // Fetch cat images !!!
  const fetchCatImages = async () => {
    // setIsLoading(true); 
    // setError(null);
    try {
      const response = await fetch('https://api.thecatapi.com/v1/images/search?limit=3');
      const data: CatImage[] = await response.json();

      // Add new cats to existing ones (so they accumulate)
      setCatImages(prevCats => [...prevCats, ...data]);

    } catch (err) {
      // Handle any errors
      setError(err instanceof Error ? err.message : 'Something went wrong!');
    } finally {
      // Always stop loading
      setIsLoading(false);
    }
  };

  // Function to vote on a cat image (upvote only)
  const voteOnCat = async (imageId: string) => {
    try {
      console.log('🔍 Voting on image:', imageId);
      console.log('🔍 API Key:', API_KEY);

      const response = await fetch('https://api.thecatapi.com/v1/votes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': API_KEY,
        },
        body: JSON.stringify({
          image_id: imageId,
          sub_id: 'student-user', // Optional unique user ID
          value: 1, // Only upvotes for now
        }),
      });

      console.log('🔍 Vote response status:', response.status);
      console.log('🔍 Vote response headers:', response.headers);

      if (!response.ok) {
        const errorText = await response.text();
        console.log('🔍 Vote error response:', errorText);
        throw new Error(`Failed to vote: ${response.status} - ${errorText}`);
      }

      const result = await response.json();

      // Update the cat image with the vote
      setCatImages(prevCats =>
        prevCats.map(cat =>
          cat.id === imageId
            ? { ...cat, userVote: 1, voteId: result.id }
            : cat
        )
      );

      // Fetch updated vote counts
      await fetchVoteCounts();

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to vote');
    }
  };

  // Function to fetch vote counts for all images
  const fetchVoteCounts = async () => {
    try {
      console.log('🔍 Fetching vote counts...');

      // Get all votes (not just user's votes) to count total likes per image
      const response = await fetch('https://api.thecatapi.com/v1/votes?limit=100&order=DESC', {
        headers: {
          'x-api-key': API_KEY,
        },
      });

      console.log('🔍 Fetch votes response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.log('🔍 Fetch votes error response:', errorText);
        throw new Error(`Failed to fetch votes: ${response.status} - ${errorText}`);
      }

      const allVotes: Vote[] = await response.json();
      setVotes(allVotes);

      // Count likes per image and update cat images
      setCatImages(prevCats =>
        prevCats.map(cat => {
          // Find user's vote for this image
          const userVote = allVotes.find(v => v.image_id === cat.id && v.sub_id === 'student-user');

          // Count total likes for this image (all users)
          const totalLikes = allVotes.filter(v => v.image_id === cat.id && v.value === 1).length;

          return {
            ...cat,
            userVote: userVote?.value,
            voteId: userVote?.id,
            totalLikes: totalLikes
          };
        })
      );

    } catch (err) {
      console.log('Failed to fetch vote counts:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch vote counts');
    }
  };

  // Function to remove a vote
  const removeVote = async (voteId: number, imageId: string) => {
    try {
      const response = await fetch(`https://api.thecatapi.com/v1/votes/${voteId}`, {
        method: 'DELETE',
        headers: {
          'x-api-key': API_KEY,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to remove vote: ${response.status}`);
      }

      // Update the cat image to remove the vote
      setCatImages(prevCats =>
        prevCats.map(cat =>
          cat.id === imageId
            ? { ...cat, userVote: undefined, voteId: undefined }
            : cat
        )
      );

      // Fetch updated vote counts
      await fetchVoteCounts();

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to remove vote');
    }
  };

  return (
    <ParallaxScrollView
      headerBackgroundColor={{ light: '#A1CEDC', dark: '#1D3D47' }}
      headerImage={
        <Image
          source={require('@/assets/images/partial-react-logo.png')}
          style={styles.reactLogo}
        />
      }>
      <ThemedView style={styles.titleContainer}>
        <ThemedText type="title">hello how are you</ThemedText>
        <HelloWave />
      </ThemedView>
      <ThemedView style={styles.stepContainer}>
        <ThemedText type="subtitle">Step 1: Try it</ThemedText>
        <ThemedText>
          Edit <ThemedText type="defaultSemiBold">app/(tabs)/index.tsx</ThemedText> to see changes.
          Press{' '}
          <ThemedText type="defaultSemiBold">
            {Platform.select({
              ios: 'cmd + d',
              android: 'cmd + m',
              web: 'F12',
            })}
          </ThemedText>{' '}
          to open developer tools.
        </ThemedText>
      </ThemedView>
      <ThemedView style={{flexDirection: 'row', alignItems: 'center',gap: 8,}}>
        <ThemedText type="subtitle">Step 2: Explore</ThemedText>
        <ThemedText>
          {`Tap the Explore tab to learn more about what's included in this starter app.`}
        </ThemedText>
      </ThemedView>
      <ThemedView style={styles.stepContainer}>
        <ThemedText type="subtitle">Step 3: Get a fresh start</ThemedText>
        <ThemedText>
          {`When you're ready, run `}
          <ThemedText type="defaultSemiBold">npm run reset-project</ThemedText> to get a fresh{' '}
          <ThemedText type="defaultSemiBold">app</ThemedText> directory. This will move the current{' '}
          <ThemedText type="defaultSemiBold">app</ThemedText> to{' '}
          <ThemedText type="defaultSemiBold">app-example</ThemedText>.
        </ThemedText>
      </ThemedView>

      <ThemedView style={styles.stepContainer}>
        <ThemedText type="subtitle">🎨 StyleSheet Exercise</ThemedText>
        <ThemedText>
          Styling 
        </ThemedText>
        <Pressable
          style={styles.testButton}
          onPress={() => router.push('/nonexistent-page' as any)}
        >
          <ThemedText style={styles.testButtonText}>Go to 404 not found page.</ThemedText>
        </Pressable>
      </ThemedView>

      <ThemedView style={styles.stepContainer}>
        <ThemedText type="subtitle">🐱 Fetch API Exercise</ThemedText>
        <ThemedText>
          Fetch random cats.
        </ThemedText>
        <Pressable
          style={[styles.catButton, isLoading && styles.disabledButton]}
          onPress={fetchCatImages}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator color="white" size="small" />
          ) : (
            <ThemedText style={styles.testButtonText}>
              🐱 Fetch Cat Pictures ({catImages.length})
            </ThemedText>
          )}
        </Pressable>

        {error && (
          <ThemedView style={styles.errorContainer}>
            <ThemedText style={styles.errorText}>❌ Error: {error}</ThemedText>
          </ThemedView>
        )}

        <ThemedText style={styles.apiInfo}>
          API: https://api.thecatapi.com/v1/images/search
        </ThemedText>
      </ThemedView>

      {/* Display Cat Images */}
      {catImages.length > 0 && (
        <ThemedView style={styles.catsContainer}>
          <ThemedText type="subtitle">🐾 Your Cat Collection</ThemedText>
          <ThemedView style={styles.catsGrid}>
            {catImages.map((cat, index) => (
              <ThemedView key={`${cat.id}-${index}`} style={styles.catImageContainer}>
                <Image
                  source={{ uri: cat.url }}
                  style={styles.catImage}
                />
                <ThemedText style={styles.catInfo}>
                  {cat.width} × {cat.height}
                </ThemedText>

                {/* Like count */}
                <ThemedView style={styles.likeCountContainer}>
                  <ThemedText style={styles.likeCount}>
                    👍 {cat.totalLikes || 0} {cat.totalLikes === 1 ? 'like' : 'likes'}
                  </ThemedText>
                </ThemedView>

                {/* Voting button */}
                <Pressable
                  style={[
                    styles.voteButton,
                    styles.upvoteButton,
                    cat.userVote === 1 && styles.activeVote
                  ]}
                  onPress={() => {
                    if (cat.userVote === 1 && cat.voteId) {
                      removeVote(cat.voteId, cat.id);
                    } else {
                      voteOnCat(cat.id);
                    }
                  }}
                >
                  <ThemedText style={styles.voteText}>
                    {cat.userVote === 1 ? '❤️ Liked' : '🤍 Like'}
                  </ThemedText>
                </Pressable>
              </ThemedView>
            ))}
          </ThemedView>

          <ThemedView style={styles.actionButtons}>
            <Pressable
              style={styles.clearButton}
              onPress={() => setCatImages([])}
            >
              <ThemedText style={styles.clearButtonText}>🗑️ Clear All Cats</ThemedText>
            </Pressable>

            <Pressable
              style={styles.historyButton}
              onPress={fetchVoteCounts}
            >
              <ThemedText style={styles.clearButtonText}>📊 Refresh Likes</ThemedText>
            </Pressable>
          </ThemedView>
        </ThemedView>
      )}

      {/* Vote Summary */}
      {votes.length > 0 && (
        <ThemedView style={styles.voteSummaryContainer}>
          <ThemedText type="subtitle">📊 Like Summary</ThemedText>
          <ThemedText style={styles.voteSummaryText}>
            Total votes in database: {votes.length} • Your votes: {votes.filter(v => v.sub_id === 'student-user').length}
          </ThemedText>
          <ThemedText style={styles.apiExplanation}>
            API: GET /votes?limit=100&order=DESC
          </ThemedText>
        </ThemedView>
      )}

    </ParallaxScrollView>
  );
}

const styles = StyleSheet.create({
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  stepContainer: {
    gap: 8,
    marginBottom: 8,
  },
  reactLogo: {
    height: 178,
    width: 290,
    bottom: 0,
    left: 0,
    position: 'absolute',
  },
  testButton: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 12,
    alignItems: 'center',
  },
  testButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  catButton: {
    backgroundColor: '#FF6B6B',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 12,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
  },
  disabledButton: {
    backgroundColor: '#CCCCCC',
    opacity: 0.7,
  },
  errorContainer: {
    backgroundColor: '#FFE6E6',
    padding: 12,
    borderRadius: 8,
    marginTop: 12,
    borderWidth: 1,
    borderColor: '#FF6B6B',
  },
  errorText: {
    color: '#D32F2F',
    fontSize: 14,
    textAlign: 'center',
  },
  apiInfo: {
    fontSize: 12,
    fontFamily: 'monospace',
    marginTop: 8,
    textAlign: 'center',
    opacity: 0.7,
  },
  catsContainer: {
    marginTop: 20,
    padding: 16,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 107, 107, 0.1)',
  },
  catsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 12,
    marginTop: 16,
  },
  catImageContainer: {
    width: '48%',
    alignItems: 'center',
  },
  catImage: {
    width: '100%',
    height: 120,
    borderRadius: 8,
    marginBottom: 4,
  },
  catInfo: {
    fontSize: 12,
    opacity: 0.7,
    textAlign: 'center',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 16,
  },
  clearButton: {
    backgroundColor: '#757575',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    alignItems: 'center',
    flex: 1,
  },
  historyButton: {
    backgroundColor: '#9C27B0',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    alignItems: 'center',
    flex: 1,
  },
  clearButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  voteButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    alignItems: 'center',
    marginTop: 8,
  },
  upvoteButton: {
    backgroundColor: '#4CAF50',
  },
  activeVote: {
    opacity: 0.7,
    borderWidth: 2,
    borderColor: '#FFD700',
  },
  voteText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  likeCountContainer: {
    marginTop: 4,
    marginBottom: 8,
    alignItems: 'center',
  },
  likeCount: {
    fontSize: 12,
    fontWeight: '600',
    color: '#666',
  },
  voteSummaryContainer: {
    marginTop: 20,
    padding: 16,
    borderRadius: 12,
    backgroundColor: 'rgba(156, 39, 176, 0.1)',
  },
  voteSummaryText: {
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
    opacity: 0.8,
  },
  apiExplanation: {
    fontSize: 11,
    fontFamily: 'monospace',
    marginTop: 12,
    padding: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: 6,
    textAlign: 'center',
  },
  moreVotesText: {
    fontSize: 12,
    textAlign: 'center',
    marginTop: 8,
    opacity: 0.7,
    fontStyle: 'italic',
  },
  debugContainer: {
    marginTop: 12,
    padding: 12,
    backgroundColor: 'rgba(255, 193, 7, 0.1)',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 193, 7, 0.3)',
  },
  debugTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  debugText: {
    fontSize: 12,
    fontFamily: 'monospace',
    marginBottom: 4,
  },
  debugButton: {
    backgroundColor: '#FF9800',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    marginTop: 8,
    alignItems: 'center',
  },
  debugButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
});
