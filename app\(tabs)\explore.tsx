import { Image } from 'expo-image';
import { useState } from 'react';
import { Dimensions, Pressable, StyleSheet } from 'react-native';

import ParallaxScrollView from '@/components/ParallaxScrollView';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';


// Image sources for the demo
const imageOptions = [
  {
    id: 'react',
    name: 'React Logo',
    source: require('@/assets/images/react-logo.png'),
    description: 'Local React logo from assets folder'
  },
  {
    id: 'partial-react',
    name: 'Partial React Logo',
    source: require('@/assets/images/partial-react-logo.png'),
    description: 'Another local React logo variant'
  },
  {
    id: 'random1',
    name: 'Random Nature',
    source: { uri: 'https://picsum.photos/300/200?random=1' },
    description: 'Random nature image from Picsum'
  },
  {
    id: 'random2',
    name: 'Random Architecture',
    source: { uri: 'https://picsum.photos/300/200?random=2' },
    description: 'Random architecture image from Picsum'
  },
  {
    id: 'random3',
    name: 'Random Abstract',
    source: { uri: 'https://picsum.photos/300/200?random=3' },
    description: 'Random abstract image from Picsum'
  }
];

export default function DynamicImageDemo() {
  const [gridColumns, setGridColumns] = useState(2);
  const [imageSize, setImageSize] = useState('medium');
  const [borderRadius, setBorderRadius] = useState(0);
  const [selectedImage, setSelectedImage] = useState<number | null>(null);

  const screenWidth = Dimensions.get('window').width;
  const padding = 20;
  const gap = 10;
   const orientation = new ScreenOrientation();

  // Calculate image size based on grid columns and screen width
  const calculateImageSize = () => {
    const availableWidth = screenWidth - (padding * 2) - (gap * (gridColumns - 1));
    return availableWidth / gridColumns;
  };

  const toggleGridColumns = () => {
    setGridColumns(prev => prev === 2 ? 3 : prev === 3 ? 1 : 2);
  };

  const toggleImageSize = () => {
    setImageSize(prev => prev === 'small' ? 'medium' : prev === 'medium' ? 'large' : 'small');
  };

  const toggleBorderRadius = () => {
    setBorderRadius(prev => prev === 0 ? 15 : prev === 15 ? 50 : 0);
  };

  const getSizeMultiplier = () => {
    switch(imageSize) {
      case 'small': return 0.8;
      case 'large': return 1.2;
      default: return 1.0;
    }
  };

  return (
    <ParallaxScrollView
      headerBackgroundColor={{ light: '#E8F4FD', dark: '#2C3E50' }}
      headerImage={
        <IconSymbol
          size={310}
          color="#3498DB"
          name="photo"
          style={styles.headerImage}
        />
      }>

      <ThemedView style={styles.titleContainer}>
        <ThemedText type="title">🖼️ Dynamic Images</ThemedText>
      </ThemedView>


      {/* Image Grid */}
      <ThemedView style={styles.gridContainer}>
        {imageOptions.map((image, index) => {
          const imageWidth = calculateImageSize() * getSizeMultiplier();
          const imageHeight = imageWidth * 0.75;

          return (
            <Pressable
              key={image.id}
              style={[
                styles.gridItem,
                {
                  width: imageWidth,
                  marginBottom: gap,
                }
              ]}
              onPress={() => setSelectedImage(selectedImage === index ? null : index)}
            >
              <Image
                source={image.source}
                style={[
                  styles.gridImage,
                  {
                    width: imageWidth,
                    height: imageHeight,
                    borderRadius: borderRadius,
                    opacity: selectedImage === index ? 0.7 : 1,
                  }
                ]}
              />
              {selectedImage === index && (
                <ThemedView style={styles.imageOverlay}>
                  <ThemedText style={styles.overlayTitle}>{image.name}</ThemedText>
                  <ThemedText style={styles.overlayDescription}>{image.description}</ThemedText>
                </ThemedView>
              )}
            </Pressable>
          );
        })}
      </ThemedView>

      {/* Grid Controls */}
      <ThemedView style={styles.controlsContainer}>
        <ThemedText type="subtitle">🎮 Grid Controls</ThemedText>

        <ThemedView style={styles.buttonRow}>
          <Pressable style={styles.controlButton} onPress={toggleGridColumns}>
            <ThemedText style={styles.buttonText}>📱 {gridColumns} Column{gridColumns !== 1 ? 's' : ''}</ThemedText>
          </Pressable>

          <Pressable style={styles.controlButton} onPress={toggleImageSize}>
            <ThemedText style={styles.buttonText}>📏 {imageSize.charAt(0).toUpperCase() + imageSize.slice(1)} Size</ThemedText>
          </Pressable>
        </ThemedView>

        <ThemedView style={styles.buttonRow}>
          <Pressable style={styles.styleButton} onPress={toggleBorderRadius}>
            <ThemedText style={styles.buttonText}>� Border: {borderRadius}px</ThemedText>
          </Pressable>

          <Pressable style={styles.styleButton} onPress={() => setSelectedImage(null)}>
            <ThemedText style={styles.buttonText}>❌ Clear Selection</ThemedText>
          </Pressable>
        </ThemedView>

        <ThemedText style={styles.gridInfo}>
          Tap any image to see details • {imageOptions.length} total images
        </ThemedText>
      </ThemedView>

      {/* Learning Section */}
      <ThemedView style={styles.learningContainer}>
        <ThemedText type="subtitle">💡 What You're Learning</ThemedText>

        <ThemedView style={styles.learningItem}>
          <ThemedText style={styles.learningTitle}>📁 Local Images:</ThemedText>
          <ThemedText style={styles.learningText}>
            Use require('@/assets/images/filename.png') for images in your project
          </ThemedText>
        </ThemedView>

        <ThemedView style={styles.learningItem}>
          <ThemedText style={styles.learningTitle}>🌐 Remote Images:</ThemedText>
          <ThemedText style={styles.learningText}>
            Use {`{ uri: 'https://...' }`} for images from the internet
          </ThemedText>
        </ThemedView>

        <ThemedView style={styles.learningItem}>
          <ThemedText style={styles.learningTitle}>📱 Grid Layouts:</ThemedText>
          <ThemedText style={styles.learningText}>
            Create responsive grids that adapt to different column counts and screen sizes
          </ThemedText>
        </ThemedView>

        <ThemedView style={styles.learningItem}>
          <ThemedText style={styles.learningTitle}>🎨 Interactive Elements:</ThemedText>
          <ThemedText style={styles.learningText}>
            Make images tappable and show overlays with additional information
          </ThemedText>
        </ThemedView>

        <ThemedView style={styles.learningItem}>
          <ThemedText style={styles.learningTitle}>📐 Dynamic Calculations:</ThemedText>
          <ThemedText style={styles.learningText}>
            Calculate image sizes based on screen width, columns, and padding
          </ThemedText>
        </ThemedView>
      </ThemedView>

    </ParallaxScrollView>
  );
}

const styles = StyleSheet.create({
  headerImage: {
    color: '#3498DB',
    bottom: -90,
    left: -35,
    position: 'absolute',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 24,
    textAlign: 'center',
  },
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 32,
    paddingHorizontal: 20,
  },
  gridItem: {
    position: 'relative',
  },
  gridImage: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  imageOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    padding: 8,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
  },
  overlayTitle: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  overlayDescription: {
    color: 'white',
    fontSize: 12,
    opacity: 0.9,
  },
  controlsContainer: {
    marginBottom: 32,
    padding: 20,
    borderRadius: 12,
    backgroundColor: 'rgba(46, 204, 113, 0.1)',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 16,
    gap: 12,
  },
  controlButton: {
    backgroundColor: '#3498DB',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    flex: 1,
    alignItems: 'center',
  },
  styleButton: {
    backgroundColor: '#2ECC71',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    flex: 1,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  gridInfo: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 12,
    opacity: 0.7,
    fontStyle: 'italic',
  },
  learningContainer: {
    padding: 20,
    borderRadius: 12,
    backgroundColor: 'rgba(155, 89, 182, 0.1)',
    marginBottom: 20,
  },
  learningItem: {
    marginTop: 16,
    padding: 12,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
  },
  learningTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  learningText: {
    fontSize: 14,
    lineHeight: 20,
  },
});
