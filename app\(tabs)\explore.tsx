import { Image } from 'expo-image';
import { useState } from 'react';
import { Dimensions, Pressable, StyleSheet } from 'react-native';

import ParallaxScrollView from '@/components/ParallaxScrollView';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';


// Image sources for the demo
const imageOptions = [
  {
    id: 'react',
    name: 'React Logo',
    source: require('@/assets/images/react-logo.png'),
    description: 'Local React logo from assets folder'
  },
  {
    id: 'partial-react',
    name: 'Partial React Logo',
    source: require('@/assets/images/partial-react-logo.png'),
    description: 'Another local React logo variant'
  },
  {
    id: 'random1',
    name: 'Random Nature',
    source: { uri: 'https://picsum.photos/300/200?random=1' },
    description: 'Random nature image from Picsum'
  },
  {
    id: 'random2',
    name: 'Random Architecture',
    source: { uri: 'https://picsum.photos/300/200?random=2' },
    description: 'Random architecture image from Picsum'
  },
  {
    id: 'random3',
    name: 'Random Abstract',
    source: { uri: 'https://picsum.photos/300/200?random=3' },
    description: 'Random abstract image from Picsum'
  }
];

export default function DynamicImageDemo() {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [imageSize, setImageSize] = useState(200);
  const [borderRadius, setBorderRadius] = useState(0);

  const currentImage = imageOptions[currentImageIndex];
  const screenWidth = Dimensions.get('window').width;

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % imageOptions.length);
  };

  const previousImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + imageOptions.length) % imageOptions.length);
  };

  const toggleSize = () => {
    setImageSize(prev => prev === 200 ? 150 : prev === 150 ? 250 : 200);
  };

  const toggleBorderRadius = () => {
    setBorderRadius(prev => prev === 0 ? 15 : prev === 15 ? 50 : 0);
  };

  return (
    <ParallaxScrollView
      headerBackgroundColor={{ light: '#E8F4FD', dark: '#2C3E50' }}
      headerImage={
        <IconSymbol
          size={310}
          color="#3498DB"
          name="photo"
          style={styles.headerImage}
        />
      }>

      <ThemedView style={styles.titleContainer}>
        <ThemedText type="title">🖼️ Dynamic Images</ThemedText>
      </ThemedView>

      <ThemedText style={styles.description}>
        Learn how to work with images in React Native! This demo shows both local images (from your assets folder) and remote images (from the internet).
      </ThemedText>

      {/* Main Image Display */}
      <ThemedView style={styles.imageContainer}>
        <Image
          source={currentImage.source}
          style={[
            styles.mainImage,
            {
              width: imageSize,
              height: imageSize * 0.75,
              borderRadius: borderRadius
            }
          ]}
        />

        <ThemedView style={styles.imageInfo}>
          <ThemedText type="subtitle">{currentImage.name}</ThemedText>
          <ThemedText style={styles.imageDescription}>
            {currentImage.description}
          </ThemedText>
          <ThemedText style={styles.imageDetails}>
            Size: {imageSize}x{Math.round(imageSize * 0.75)} • Border: {borderRadius}px
          </ThemedText>
        </ThemedView>
      </ThemedView>

      {/* Navigation Controls */}
      <ThemedView style={styles.controlsContainer}>
        <ThemedText type="subtitle">🎮 Image Controls</ThemedText>

        <ThemedView style={styles.buttonRow}>
          <Pressable style={styles.controlButton} onPress={previousImage}>
            <ThemedText style={styles.buttonText}>← Previous</ThemedText>
          </Pressable>

          <ThemedText style={styles.imageCounter}>
            {currentImageIndex + 1} of {imageOptions.length}
          </ThemedText>

          <Pressable style={styles.controlButton} onPress={nextImage}>
            <ThemedText style={styles.buttonText}>Next →</ThemedText>
          </Pressable>
        </ThemedView>

        <ThemedView style={styles.buttonRow}>
          <Pressable style={styles.styleButton} onPress={toggleSize}>
            <ThemedText style={styles.buttonText}>📏 Change Size</ThemedText>
          </Pressable>

          <Pressable style={styles.styleButton} onPress={toggleBorderRadius}>
            <ThemedText style={styles.buttonText}>🔄 Toggle Corners</ThemedText>
          </Pressable>
        </ThemedView>
      </ThemedView>

      {/* Learning Section */}
      <ThemedView style={styles.learningContainer}>
        <ThemedText type="subtitle">💡 What You're Learning</ThemedText>

        <ThemedView style={styles.learningItem}>
          <ThemedText style={styles.learningTitle}>📁 Local Images:</ThemedText>
          <ThemedText style={styles.learningText}>
            Use require('@/assets/images/filename.png') for images in your project
          </ThemedText>
        </ThemedView>

        <ThemedView style={styles.learningItem}>
          <ThemedText style={styles.learningTitle}>🌐 Remote Images:</ThemedText>
          <ThemedText style={styles.learningText}>
            Use {`{ uri: 'https://...' }`} for images from the internet
          </ThemedText>
        </ThemedView>

        <ThemedView style={styles.learningItem}>
          <ThemedText style={styles.learningTitle}>🎨 Dynamic Styling:</ThemedText>
          <ThemedText style={styles.learningText}>
            Change image size, borders, and other properties with state
          </ThemedText>
        </ThemedView>

        <ThemedView style={styles.learningItem}>
          <ThemedText style={styles.learningTitle}>📱 Responsive Design:</ThemedText>
          <ThemedText style={styles.learningText}>
            Use Dimensions to make images look good on all screen sizes
          </ThemedText>
        </ThemedView>
      </ThemedView>

    </ParallaxScrollView>
  );
}

const styles = StyleSheet.create({
  headerImage: {
    color: '#3498DB',
    bottom: -90,
    left: -35,
    position: 'absolute',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 24,
    textAlign: 'center',
  },
  imageContainer: {
    alignItems: 'center',
    marginBottom: 32,
    padding: 20,
    borderRadius: 12,
    backgroundColor: 'rgba(52, 152, 219, 0.1)',
  },
  mainImage: {
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  imageInfo: {
    alignItems: 'center',
    gap: 8,
  },
  imageDescription: {
    fontSize: 14,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  imageDetails: {
    fontSize: 12,
    textAlign: 'center',
    opacity: 0.7,
  },
  controlsContainer: {
    marginBottom: 32,
    padding: 20,
    borderRadius: 12,
    backgroundColor: 'rgba(46, 204, 113, 0.1)',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 16,
    gap: 12,
  },
  controlButton: {
    backgroundColor: '#3498DB',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    flex: 1,
    alignItems: 'center',
  },
  styleButton: {
    backgroundColor: '#2ECC71',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    flex: 1,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  imageCounter: {
    fontSize: 16,
    fontWeight: '600',
    minWidth: 80,
    textAlign: 'center',
  },
  learningContainer: {
    padding: 20,
    borderRadius: 12,
    backgroundColor: 'rgba(155, 89, 182, 0.1)',
    marginBottom: 20,
  },
  learningItem: {
    marginTop: 16,
    padding: 12,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
  },
  learningTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  learningText: {
    fontSize: 14,
    lineHeight: 20,
  },
});
