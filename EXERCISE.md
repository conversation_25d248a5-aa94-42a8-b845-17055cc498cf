# React Native Beginner Exercise: Personal Profile App

Welcome to your first React Native exercise! 🎉 In this exercise, you'll transform the starter app into a simple personal profile app. Don't worry - we'll take it step by step!

## What You'll Learn
- How to edit text and content in React Native
- Basic styling with StyleSheet
- Working with images
- Creating simple interactive elements
- Understanding React Native components

## Before You Start

1. Make sure the app is running:
   ```bash
   npm install
   npx expo start
   ```

2. Open the app in your preferred platform (web browser is easiest for beginners)

3. You should see a "Welcome!" screen with some example content

## Exercise Tasks

### Task 1: Personalize the Welcome Screen (Easy)
**File to edit:** `app/(tabs)/index.tsx`

**Goal:** Replace the generic welcome message with your own personal information.

**What to do:**
1. Change "Welcome!" to "Hi, I'm [Your Name]!"
2. Update the step descriptions to be about yourself:
   - Step 1: Change to "About Me" and add a short description about yourself
   - Step 2: Change to "My Interests" and list 2-3 things you enjoy
   - Step 3: Change to "Contact Me" and add your email or social media

**Hint:** Look for `<ThemedText>` components and change the text between the opening and closing tags.

### Task 2: Add Your Photo (Medium)
**Files to work with:** `app/(tabs)/index.tsx` and add an image to `assets/images/`

**Goal:** Replace the React logo with your own photo.

**What to do:**
1. Add your photo to the `assets/images/` folder (name it `profile-photo.jpg` or similar)
2. In `index.tsx`, find the `<Image>` component that shows the React logo
3. Change the `source` to point to your photo: `require('@/assets/images/profile-photo.jpg')`
4. Adjust the `styles.reactLogo` to make your photo look good (try changing height and width)

**Hint:** If you don't have a photo, you can download a placeholder from https://picsum.photos/290/178

### Task 3: Create a Skills Section (Medium)
**File to edit:** `app/(tabs)/index.tsx`

**Goal:** Add a new section that lists your skills or hobbies.

**What to do:**
1. After the last `<ThemedView style={styles.stepContainer}>`, add a new section
2. Create a list of 3-5 skills or hobbies
3. Use `<ThemedText type="subtitle">` for the section title
4. Use regular `<ThemedText>` for each skill

**Example structure:**
```tsx
<ThemedView style={styles.stepContainer}>
  <ThemedText type="subtitle">My Skills</ThemedText>
  <ThemedText>• JavaScript</ThemedText>
  <ThemedText>• Problem Solving</ThemedText>
  <ThemedText>• Team Work</ThemedText>
</ThemedView>
```

### Task 4: Style Your Profile (Medium-Hard)
**File to edit:** `app/(tabs)/index.tsx`

**Goal:** Customize the colors and spacing to make it your own.

**What to do:**
1. In the `styles` object at the bottom, try modifying:
   - `gap` values to change spacing
   - `marginBottom` to adjust section spacing
2. Add a new style for your skills section
3. Experiment with the `headerBackgroundColor` in `ParallaxScrollView` to change the header color

**Example new style:**
```tsx
skillsContainer: {
  gap: 4,
  marginBottom: 16,
  padding: 16,
},
```

### Task 5: Make It Interactive (Hard)
**File to edit:** `app/(tabs)/index.tsx`

**Goal:** Add a button that shows/hides additional information about you.

**What to do:**
1. Import `useState` from React: `import { useState } from 'react';`
2. Import `Pressable` from react-native: `import { Platform, StyleSheet, Pressable } from 'react-native';`
3. Add state to track if extra info is shown:
   ```tsx
   const [showExtraInfo, setShowExtraInfo] = useState(false);
   ```
4. Create a button that toggles the state:
   ```tsx
   <Pressable 
     style={styles.button} 
     onPress={() => setShowExtraInfo(!showExtraInfo)}
   >
     <ThemedText>{showExtraInfo ? 'Hide' : 'Show'} More About Me</ThemedText>
   </Pressable>
   ```
5. Add conditional content that only shows when `showExtraInfo` is true
6. Style your button in the `styles` object

## Bonus Challenges (Optional)

### Bonus 1: Customize the Explore Tab
**File to edit:** `app/(tabs)/explore.tsx`

Replace the generic information with details about your learning journey or projects you want to build.

### Bonus 2: Add Icons
Research how to use `@expo/vector-icons` to add icons next to your skills or contact information.

### Bonus 3: Improve the Layout
Experiment with different `flexDirection`, `alignItems`, and `justifyContent` properties in your styles.

## Getting Help

- **Stuck?** Check the existing code in the project for examples
- **Errors?** Read the error message carefully - it usually tells you what's wrong
- **Want to experiment?** Try changing one thing at a time and see what happens
- **Need inspiration?** Look at the `components/` folder to see how other components are built

## Submission

When you're done:
1. Take a screenshot of your app running
2. Share your `app/(tabs)/index.tsx` file
3. Write 2-3 sentences about what you learned

## What's Next?

After completing this exercise, you'll have hands-on experience with:
- React Native components and JSX
- Basic styling and layout
- State management with useState
- Event handling with onPress
- Working with images and assets

Great job on your first React Native app! 🚀

---

**Remember:** Don't be afraid to experiment! The best way to learn React Native is by trying things out. If something breaks, you can always undo your changes or ask for help.
