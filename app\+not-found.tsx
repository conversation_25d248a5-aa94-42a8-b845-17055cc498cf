import { Link, Stack } from 'expo-router';
import { StyleSheet } from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';

export default function NotFoundScreen() {
  return (
    <>
      <Stack.Screen options={{ title: 'Oops!' }} />
      <ThemedView style={styles.container}>
        <ThemedText type="title">This screen does not exist.</ThemedText>
        <Link href="/" style={styles.button}>
          <ThemedText type="link" style={styles.buttonText}>Go to home screen!</ThemedText>
        </Link>
      </ThemedView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  link: {
    marginTop: 15,
    paddingVertical: 15,
  },button: {

  backgroundColor: '#4CAF50',

  paddingHorizontal: 20,

  paddingVertical: 12,

  borderRadius: 4,

  marginTop: 20,

  shadowColor: '#000',

  shadowOffset: { width: 0, height: 2 },

  shadowOpacity: 0.25,

  shadowRadius: 3.84,

  elevation: 5,

},buttonText: {

  color: 'white',

  fontSize: 16,

  fontWeight: 'bold',

  textAlign: 'center',

},
});
