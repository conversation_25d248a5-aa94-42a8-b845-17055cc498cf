# React Native StyleSheet Exercise for Complete Beginners

Welcome to your StyleSheet adventure! 🎨 In this exercise, you'll learn how to make your React Native app look amazing using styles. Think of StyleSheet as your digital paintbrush!

## What You'll Learn
- How StyleSheet works in React Native
- Basic layout properties (margin, padding, alignment)
- Text styling (fonts, colors, sizes)
- Background colors and borders
- Flexbox basics for positioning elements
- How to organize and reuse styles

## Before You Start

1. Make sure your app is running:
   ```bash
   npx expo start
   ```

2. We'll be working mainly with the **404 Not Found page** (`app/+not-found.tsx`) because it's simple and perfect for learning!

3. To access the 404 page easily:
   - **On your phone/device**: Look for the green "Go to StyleSheet Exercise Page" button on the home screen and tap it
   - **On web**: You can also manually go to a URL that doesn't exist (like adding `/test` to the end of your URL)

## Understanding the Basics

### What is StyleSheet?
StyleSheet in React Native is like CSS for websites, but specifically designed for mobile apps. It helps you:
- Make text bigger or smaller
- Change colors
- Position elements on screen
- Add spacing between items
- Create beautiful layouts

### How Styles Work
```tsx
// 1. Import StyleSheet
import { StyleSheet } from 'react-native';

// 2. Create your styles
const styles = StyleSheet.create({
  myStyle: {
    fontSize: 20,
    color: 'blue',
  },
});

// 3. Apply to components
<Text style={styles.myStyle}>Hello!</Text>
```

## Exercise Tasks

### Task 1: Text Styling Playground (Easy)
**File to edit:** `app/+not-found.tsx`

**Goal:** Learn how to style text by customizing the error message.

**What to do:**
1. In the `styles` object, add a new style called `errorText`:
   ```tsx
   errorText: {
     fontSize: 24,
     color: 'red',
     fontWeight: 'bold',
     textAlign: 'center',
   },
   ```

2. Apply this style to the "This screen does not exist." text:
   ```tsx
   <ThemedText type="title" style={styles.errorText}>This screen does not exist.</ThemedText>
   ```

3. **Experiment time!** Try changing:
   - `fontSize` to different numbers (16, 28, 36)
   - `color` to different colors ('blue', 'green', '#FF6B6B', '#4ECDC4')
   - `fontWeight` to 'normal', '600', or '800'
   - `textAlign` to 'left' or 'right'

**What you're learning:** Basic text properties and how colors work in React Native.

### Task 2: Container Styling (Easy-Medium)
**File to edit:** `app/+not-found.tsx`

**Goal:** Learn about spacing, backgrounds, and container properties.

**What to do:**
1. Modify the existing `container` style to make it more interesting:
   ```tsx
   container: {
     flex: 1,
     alignItems: 'center',
     justifyContent: 'center',
     padding: 20,
     backgroundColor: '#F0F8FF',  // Light blue background
     margin: 10,
   },
   ```

2. **Experiment with these properties:**
   - `backgroundColor`: Try '#FFE4E1', '#F0FFF0', '#FFF8DC'
   - `padding`: Try 10, 30, 50 (space inside the container)
   - `margin`: Try 0, 20, 40 (space outside the container)
   - `alignItems`: Try 'flex-start', 'flex-end'
   - `justifyContent`: Try 'flex-start', 'flex-end', 'space-between'

**What you're learning:** How containers work and the difference between padding and margin.

### Task 3: Button Styling (Medium)
**File to edit:** `app/+not-found.tsx`

**Goal:** Create a beautiful button style for the "Go to home screen!" link.

**What to do:**
1. Replace the existing `link` style with a proper button style:
   ```tsx
   button: {
     backgroundColor: '#4CAF50',
     paddingHorizontal: 20,
     paddingVertical: 12,
     borderRadius: 8,
     marginTop: 20,
     shadowColor: '#000',
     shadowOffset: { width: 0, height: 2 },
     shadowOpacity: 0.25,
     shadowRadius: 3.84,
     elevation: 5,
   },
   buttonText: {
     color: 'white',
     fontSize: 16,
     fontWeight: 'bold',
     textAlign: 'center',
   },
   ```

2. Update your JSX to use these new styles:
   ```tsx
   <Link href="/" style={styles.button}>
     <ThemedText style={styles.buttonText}>Go to home screen!</ThemedText>
   </Link>
   ```

3. **Experiment with:**
   - `backgroundColor`: Try '#FF6B6B', '#4ECDC4', '#45B7D1'
   - `borderRadius`: Try 0, 15, 25 (how rounded the corners are)
   - `paddingHorizontal` and `paddingVertical`: Try different numbers
   - `shadowOpacity`: Try 0.1, 0.5, 0.8 (how strong the shadow is)

**What you're learning:** Advanced styling with shadows, borders, and padding variations.

### Task 4: Layout Practice (Medium)
**File to edit:** `app/+not-found.tsx`

**Goal:** Add more elements and practice layout with Flexbox.

**What to do:**
1. Add more content to your not-found page. Replace the JSX inside `<ThemedView style={styles.container}>`:
   ```tsx
   <ThemedView style={styles.container}>
     <ThemedView style={styles.iconContainer}>
       <ThemedText style={styles.icon}>🤔</ThemedText>
     </ThemedView>
     
     <ThemedText type="title" style={styles.errorText}>
       Oops! Page Not Found
     </ThemedText>
     
     <ThemedText style={styles.description}>
       The page you're looking for doesn't exist. But don't worry, we'll help you get back on track!
     </ThemedText>
     
     <Link href="/" style={styles.button}>
       <ThemedText style={styles.buttonText}>Take Me Home</ThemedText>
     </Link>
   </ThemedView>
   ```

2. Add these new styles:
   ```tsx
   iconContainer: {
     marginBottom: 20,
   },
   icon: {
     fontSize: 60,
     textAlign: 'center',
   },
   description: {
     fontSize: 16,
     textAlign: 'center',
     marginHorizontal: 20,
     marginBottom: 30,
     lineHeight: 24,
     color: '#666',
   },
   ```

**What you're learning:** How to create complex layouts with multiple elements and proper spacing.

### Task 5: Responsive Design (Hard)
**File to edit:** `app/+not-found.tsx`

**Goal:** Make your design look good on different screen sizes.

**What to do:**
1. Import `Dimensions` to get screen information:
   ```tsx
   import { StyleSheet, Dimensions } from 'react-native';
   ```

2. Get screen width and create responsive styles:
   ```tsx
   const { width } = Dimensions.get('window');
   
   const styles = StyleSheet.create({
     container: {
       flex: 1,
       alignItems: 'center',
       justifyContent: 'center',
       padding: width > 400 ? 40 : 20,  // More padding on larger screens
       backgroundColor: '#F0F8FF',
     },
     errorText: {
       fontSize: width > 400 ? 28 : 24,  // Bigger text on larger screens
       color: '#FF6B6B',
       fontWeight: 'bold',
       textAlign: 'center',
       marginBottom: 20,
     },
     // ... rest of your styles
   });
   ```

**What you're learning:** How to make your app look good on phones, tablets, and different screen sizes.

## Bonus Challenges

### Bonus 1: Color Themes
Create multiple color themes and switch between them:
```tsx
const themes = {
  light: { background: '#FFFFFF', text: '#000000' },
  dark: { background: '#1A1A1A', text: '#FFFFFF' },
  colorful: { background: '#FF6B6B', text: '#FFFFFF' },
};
```

### Bonus 2: Animation Preparation
Add styles that will be ready for animations:
```tsx
animatedContainer: {
  transform: [{ scale: 1 }],
  opacity: 1,
},
```

### Bonus 3: Advanced Layouts
Try creating a card-like design with borders and multiple sections.

## Common StyleSheet Properties Reference

### Text Properties:
- `fontSize`: 16, 20, 24 (size of text)
- `fontWeight`: 'normal', 'bold', '600'
- `color`: 'red', '#FF0000', 'rgb(255, 0, 0)'
- `textAlign`: 'left', 'center', 'right'
- `lineHeight`: 20, 24 (space between lines)

### Layout Properties:
- `flex`: 1 (takes available space)
- `flexDirection`: 'row', 'column'
- `alignItems`: 'center', 'flex-start', 'flex-end'
- `justifyContent`: 'center', 'space-between', 'flex-start'

### Spacing Properties:
- `margin`: 10, 20 (space outside element)
- `padding`: 10, 20 (space inside element)
- `marginTop`, `marginBottom`, `marginLeft`, `marginRight`
- `paddingHorizontal`, `paddingVertical`

### Visual Properties:
- `backgroundColor`: 'white', '#FF0000'
- `borderRadius`: 5, 10, 15 (rounded corners)
- `borderWidth`: 1, 2
- `borderColor`: 'gray', '#CCCCCC'

## Tips for Success

1. **Start small** - Change one property at a time
2. **Save often** - See your changes immediately
3. **Use the color picker** - Try online tools for hex colors
4. **Don't be afraid to break things** - You can always undo!
5. **Look at existing styles** - Learn from the components already in the project

## What's Next?

After this exercise, you'll understand:
- How to create and apply styles
- Basic layout principles
- Color and typography basics
- Spacing and positioning
- How to make responsive designs

Great job learning React Native styling! 🎨✨
